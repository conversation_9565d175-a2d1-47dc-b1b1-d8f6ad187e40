# Projet D313 - Boutique en ligne PrestaShop
## Développement d'une solution e-commerce complète

**Auteur :** Étudiant D313  
**Encadrant :** Frédéric Guidec - UBS Vannes  
**Date :** 2025  
**Version :** 1.0  

---

## 📋 Analyse du cahier des charges

### Objectifs pédagogiques
Ce projet vise à maîtriser :
- Le déploiement et la configuration d'une solution e-commerce
- La personnalisation de thèmes PrestaShop
- Le développement de modules spécifiques
- L'optimisation des performances web
- La méthodologie de tests et validation

### Architecture technique retenue

```
Projet PrestaShop D313/
├── documentation/           # Documentation technique et rapport
├── theme/                  # Thème personnalisé mon-theme-prestashop-d313
│   ├── assets/
│   ├── templates/
│   └── config/
├── modules/                # Modules développés
│   └── imprimantes/       # Module de gestion des imprimantes
├── data/                  # Fichiers de données
│   ├── produits.csv
│   ├── imprimantes.csv
│   └── liens-produits-imprimantes.csv
├── tests/                 # Scripts et résultats de tests
└── performance/           # Analyses de performance
```

---

## 🎯 Méthodologie de développement (Style F. Guidec)

### Principes directeurs
1. **Documentation exhaustive** : Chaque étape est documentée avec justifications techniques
2. **Approche incrémentale** : Développement par phases avec validation à chaque étape
3. **Tests systématiques** : Validation fonctionnelle et technique continue
4. **Analyse critique** : Évaluation des choix techniques et alternatives

### Standards de codage
- Commentaires détaillés en français
- Nommage explicite des variables et fonctions
- Respect des conventions PrestaShop
- Gestion d'erreurs robuste

---

## 📊 Planification détaillée

### Phase 1 : Infrastructure (2 pts)
- [x] Analyse des exigences
- [ ] Déploiement PrestaShop sur serveur
- [ ] Configuration accès administrateur
- [ ] Mise en place avertissement "boutique école"

### Phase 2 : Personnalisation (1 pt)
- [ ] Création thème mon-theme-prestashop-d313
- [ ] Modification CSS (barre orange)
- [ ] Configuration logo et favicon

### Phase 3 : Configuration fonctionnelle (1 pt)
- [ ] Systèmes de paiement (chèque, virement)
- [ ] Réécriture d'URL
- [ ] Modes de livraison

### Phase 4 : Modules et données (3 pts)
- [ ] Installation module 1-click-upgrade
- [ ] Import catalogue produits
- [ ] Configuration emails

### Phase 5 : Interface utilisateur (2 pts)
- [ ] Optimisation FrontOffice
- [ ] Tests processus de commande

### Phase 6 : Performance (2 pts)
- [ ] Analyse performances
- [ ] Optimisations techniques

### Phase 7 : Développement avancé (5 pts)
- [ ] Module imprimantes personnalisé

---

## 🔧 Environnement technique

### Prérequis serveur
- PHP 7.4+ / 8.x
- MySQL 5.7+ / MariaDB 10.2+
- Apache/Nginx avec mod_rewrite
- Extension PHP : curl, dom, fileinfo, gd, intl, json, mbstring, openssl, pdo_mysql, simplexml, zip

### Outils de développement
- IDE : VSCode avec extensions PrestaShop
- Gestionnaire de versions : Git
- Tests performance : webpagetest.org
- Documentation : Markdown + PDF

---

## 📝 Notes de développement

*Cette section sera enrichie au fur et à mesure du développement avec les observations, difficultés rencontrées et solutions apportées, selon la méthodologie de documentation continue préconisée par F. Guidec.*

---

**Remarque importante :** Cette boutique est développée à des fins pédagogiques uniquement. Aucune transaction réelle ne sera effectuée.
