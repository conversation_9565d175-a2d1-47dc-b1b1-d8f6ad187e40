# Analyse technique détaillée - Projet D313
## Méthodologie F. Guidec - UBS Vannes

---

## 1. Analyse des exigences fonctionnelles

### 1.1 Exigences critiques (Priorité 1)
```
REQ-001 : Déploiement PrestaShop fonctionnel
- Critère : Boutique sans erreur
- Validation : Tests de navigation complète
- Points : 2/15

REQ-002 : Accès administrateur tuteur
- Critère : Connexion réussie
- Validation : Test de connexion
- Points : 0.5/15

REQ-003 : Avertissement "boutique école"
- Critère : Visible sur toutes les pages
- Validation : Contrôle visuel systématique
- Points : 0.5/15
```

### 1.2 Exigences de personnalisation (Priorité 2)
```
REQ-004 : Thème personnalisé mon-theme-prestashop-d313
- Base : Thème "classic"
- Modifications : <PERSON><PERSON> orange, logo, favicon
- Validation : Contrôle visuel et code source
- Points : 1/15
```

### 1.3 Exigences de configuration (Priorité 2)
```
REQ-005 : Systèmes de paiement
- Méthodes : Chèque, virement
- Extension possible : Paybox (mode test)
- Validation : Test de commande complète

REQ-006 : Modes de livraison
- Configuration : 2 modes minimum (dont livraison rapide)
- Suppression : Retrait en magasin
- Validation : Test de commande

REQ-007 : Réécriture d'URL
- Objectif : URLs SEO-friendly
- Validation : Contrôle structure URLs
- Points total section : 1/15
```

---

## 2. Architecture technique retenue

### 2.1 Choix technologiques justifiés

#### PrestaShop Open Source
**Justification :**
- Solution mature et éprouvée
- Large communauté de développeurs
- Architecture modulaire extensible
- Documentation complète disponible

**Alternatives évaluées :**
- WooCommerce : Écarté (dépendance WordPress)
- Magento : Écarté (complexité excessive pour le projet)
- Shopify : Écarté (solution propriétaire)

#### Structure modulaire
```
Architecture en couches :
┌─────────────────────────────────┐
│        Couche Présentation      │ ← Thème personnalisé
├─────────────────────────────────┤
│        Couche Métier           │ ← Modules PrestaShop
├─────────────────────────────────┤
│        Couche Données          │ ← Base MySQL
└─────────────────────────────────┘
```

### 2.2 Stratégie de développement

#### Approche incrémentale (Méthode Guidec)
1. **Phase d'installation** : Base fonctionnelle
2. **Phase de personnalisation** : Adaptation visuelle
3. **Phase de configuration** : Fonctionnalités métier
4. **Phase de développement** : Module spécifique
5. **Phase d'optimisation** : Performance et tests

#### Gestion des risques identifiés
```
RISQUE-001 : Incompatibilité serveur
- Probabilité : Faible
- Impact : Critique
- Mitigation : Tests préalables environnement

RISQUE-002 : Corruption données import
- Probabilité : Moyenne
- Impact : Moyen
- Mitigation : Sauvegarde avant import + validation

RISQUE-003 : Performance dégradée
- Probabilité : Moyenne
- Impact : Moyen
- Mitigation : Monitoring continu + optimisations
```

---

## 3. Spécifications techniques détaillées

### 3.1 Module imprimantes (5 points - Développement critique)

#### Analyse fonctionnelle
```
Objectif : Associer consommables ↔ imprimantes
Données d'entrée :
- imprimantes.csv : Liste des imprimantes
- liens-produits-imprimantes.csv : Relations

Fonctionnalités requises :
1. Import et stockage données imprimantes
2. Interface de recherche imprimantes
3. Affichage consommables compatibles
4. Recherche bidirectionnelle (produit→imprimante, imprimante→produit)
```

#### Architecture technique module
```php
modules/imprimantes/
├── imprimantes.php              // Classe principale
├── config.xml                   // Configuration module
├── install/
│   └── install.sql              // Structure BDD
├── controllers/
│   ├── front/                   // Contrôleurs front-office
│   └── admin/                   // Contrôleurs back-office
├── views/
│   ├── templates/front/         // Templates front
│   └── templates/admin/         // Templates admin
└── classes/
    ├── Imprimante.php           // Modèle imprimante
    └── LienProduitImprimante.php // Modèle relation
```

#### Modèle de données
```sql
-- Table imprimantes
CREATE TABLE ps_imprimante (
    id_imprimante INT AUTO_INCREMENT PRIMARY KEY,
    marque VARCHAR(100) NOT NULL,
    modele VARCHAR(100) NOT NULL,
    reference VARCHAR(50) UNIQUE,
    description TEXT,
    active TINYINT(1) DEFAULT 1,
    date_add DATETIME NOT NULL,
    date_upd DATETIME NOT NULL
);

-- Table relations produits-imprimantes
CREATE TABLE ps_produit_imprimante (
    id_produit INT NOT NULL,
    id_imprimante INT NOT NULL,
    PRIMARY KEY (id_produit, id_imprimante),
    FOREIGN KEY (id_produit) REFERENCES ps_product(id_product),
    FOREIGN KEY (id_imprimante) REFERENCES ps_imprimante(id_imprimante)
);
```

---

## 4. Plan de tests et validation

### 4.1 Tests unitaires
- Validation import données
- Fonctions de recherche
- Intégrité relations BDD

### 4.2 Tests d'intégration
- Processus de commande complet
- Compatibilité thème personnalisé
- Performance globale

### 4.3 Tests utilisateur
- Navigation intuitive
- Recherche efficace
- Processus d'achat fluide

---

**Note méthodologique :** Cette analyse suit les principes de rigueur et d'exhaustivité préconisés par F. Guidec, avec une approche systémique et une documentation continue du processus de développement.
